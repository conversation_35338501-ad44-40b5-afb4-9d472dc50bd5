#!/usr/bin/env python3
"""
Windows环境下设置osam模型文件的脚本

此脚本会：
1. 创建必要的缓存目录
2. 列出所有需要的模型文件和对应的哈希值
3. 检查已存在的模型文件
4. 提供下载链接和放置位置的说明

使用方法：
python setup_models_windows.py
"""

import os
import hashlib
import sys
from pathlib import Path


def get_cache_dir():
    """获取Windows下的缓存目录"""
    if os.name == 'nt':  # Windows
        cache_dir = os.path.expanduser("~/.cache/osam/models/blobs")
    else:
        cache_dir = os.path.expanduser("~/.cache/osam/models/blobs")
    return cache_dir


def create_cache_dir():
    """创建缓存目录"""
    cache_dir = get_cache_dir()
    os.makedirs(cache_dir, exist_ok=True)
    print(f"缓存目录: {cache_dir}")
    return cache_dir


def get_file_hash(filepath):
    """计算文件的SHA256哈希值"""
    sha256_hash = hashlib.sha256()
    try:
        with open(filepath, 'rb') as f:
            for byte_block in iter(lambda: f.read(4096), b''):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    except Exception:
        return None


def check_model_files():
    """检查模型文件状态"""
    # 定义所有模型的信息
    models = {
        "Sam100m": {
            "encoder": {
                "url": "https://github.com/wkentaro/labelme/releases/download/sam-20230416/sam_vit_b_01ec64.quantized.encoder.onnx",
                "hash": "sha256:3346b9cc551c9902fbf3b203935e933592b22e042365f58321c17fc12641fd6a"
            },
            "decoder": {
                "url": "https://github.com/wkentaro/labelme/releases/download/sam-20230416/sam_vit_b_01ec64.quantized.decoder.onnx", 
                "hash": "sha256:edbcf1a0afaa55621fb0abe6b3db1516818b609ea9368f309746a3afc68f7613"
            }
        },
        "Sam300m": {
            "encoder": {
                "url": "https://github.com/wkentaro/labelme/releases/download/sam-20230416/sam_vit_l_0b3195.quantized.encoder.onnx",
                "hash": "sha256:f7158a4a1fe7f670ef47ea2f7f852685425c1ed6caa40f5df86cbe2b0502034f"
            },
            "decoder": {
                "url": "https://github.com/wkentaro/labelme/releases/download/sam-20230416/sam_vit_l_0b3195.quantized.decoder.onnx",
                "hash": "sha256:552ebb23bf52c5e5b971ac710d1eb8dccfd88b36cc6aff881d1536d1662e6d7b"
            }
        },
        "Sam600m": {
            "encoder": {
                "url": "https://github.com/wkentaro/labelme/releases/download/sam-20230416/sam_vit_h_4b8939.quantized.encoder.onnx",
                "hash": "sha256:a5c745fd4279efc5e5436b412200e983dafc2249ce172af6cc0002a71bb5f485"
            },
            "decoder": {
                "url": "https://github.com/wkentaro/labelme/releases/download/sam-20230416/sam_vit_h_4b8939.quantized.decoder.onnx",
                "hash": "sha256:020b385a45ffe51097e1acd10cd791075a86171153505f789a793bc382eef210"
            }
        },
        "Sam2Tiny": {
            "encoder": {
                "url": "https://github.com/wkentaro/osam/releases/download/sam2.1/sam2.1_tiny_preprocess.onnx",
                "hash": "sha256:5557482c56565f6a6c8206874b1a11c392cef8a1766477bf035b919092f2b619"
            },
            "decoder": {
                "url": "https://github.com/wkentaro/osam/releases/download/sam2.1/sam2.1_tiny.onnx",
                "hash": "sha256:11a2c86fabbea9d0268213a9205c99a7f7e379caa0493bd13f5cca8ffaae6777"
            }
        },
        "Sam2Small": {
            "encoder": {
                "url": "https://github.com/wkentaro/osam/releases/download/sam2.1/sam2.1_small_preprocess.onnx",
                "hash": "sha256:06016c6dfb146ce10e4dadfdf49e88a05c8d1f97a6b7c57e150e60d2d46a72e7"
            },
            "decoder": {
                "url": "https://github.com/wkentaro/osam/releases/download/sam2.1/sam2.1_small.onnx",
                "hash": "sha256:153aaef5047a3b95285d90cbb39dad6c7b5821bfd944dbf56483f3f735936941"
            }
        },
        "Sam2BasePlus": {
            "encoder": {
                "url": "https://github.com/wkentaro/osam/releases/download/sam2.1/sam2.1_base_plus_preprocess.onnx",
                "hash": "sha256:ce95c44082b4532c25ae01e11da3c9337dab7b04341455c09ae599dc9ae5c438"
            },
            "decoder": {
                "url": "https://github.com/wkentaro/osam/releases/download/sam2.1/sam2.1_base_plus.onnx",
                "hash": "sha256:2ad091af889b20ad2035503b4355cd8924fcf0e29fa6536924c48dc220ecdc56"
            }
        },
        "Sam2Large": {
            "encoder": {
                "url": "https://github.com/wkentaro/osam/releases/download/sam2.1/sam2.1_large_preprocess.onnx",
                "hash": "sha256:ab676f957528918496990f242163fd6b41a7222ae255862e846d9ab35115c12e"
            },
            "decoder": {
                "url": "https://github.com/wkentaro/osam/releases/download/sam2.1/sam2.1_large.onnx",
                "hash": "sha256:a3ebc6b8e254bd4ca1346901b9472bc2fae9e827cfd67d67e162d0ae2b1ec9a0"
            }
        },
        "EfficientSam10m": {
            "encoder": {
                "url": "https://github.com/labelmeai/efficient-sam/releases/download/onnx-models-20231225/efficient_sam_vitt_encoder.onnx",
                "hash": "sha256:7a73ee65aa2c37237c89b4b18e73082f757ffb173899609c5d97a2bbd4ebb02d"
            },
            "decoder": {
                "url": "https://github.com/labelmeai/efficient-sam/releases/download/onnx-models-20231225/efficient_sam_vitt_decoder.onnx",
                "hash": "sha256:e1afe46232c3bfa3470a6a81c7d3181836a94ea89528aff4e0f2d2c611989efd"
            }
        },
        "EfficientSam30m": {
            "encoder": {
                "url": "https://github.com/labelmeai/efficient-sam/releases/download/onnx-models-20231225/efficient_sam_vits_encoder.onnx",
                "hash": "sha256:4cacbb23c6903b1acf87f1d77ed806b840800c5fcd4ac8f650cbffed474b8896"
            },
            "decoder": {
                "url": "https://github.com/labelmeai/efficient-sam/releases/download/onnx-models-20231225/efficient_sam_vits_decoder.onnx",
                "hash": "sha256:4727baf23dacfb51d4c16795b2ac382c403505556d0284e84c6ff3d4e8e36f22"
            }
        }
    }
    
    cache_dir = create_cache_dir()
    
    print("="*80)
    print("OSAM模型文件检查报告")
    print("="*80)
    
    total_models = 0
    available_models = 0
    
    for model_name, components in models.items():
        print(f"\n{model_name}:")
        model_complete = True
        
        for component_name, info in components.items():
            total_models += 1
            hash_value = info["hash"]
            file_path = os.path.join(cache_dir, hash_value)
            
            if os.path.exists(file_path):
                # 验证哈希值
                actual_hash = get_file_hash(file_path)
                expected_hash = hash_value.replace("sha256:", "")
                
                if actual_hash == expected_hash:
                    print(f"  ✓ {component_name}: 文件存在且哈希值正确")
                    available_models += 1
                else:
                    print(f"  ✗ {component_name}: 文件存在但哈希值不匹配")
                    print(f"    期望: {expected_hash}")
                    print(f"    实际: {actual_hash}")
                    model_complete = False
            else:
                print(f"  ✗ {component_name}: 文件不存在")
                print(f"    需要下载: {info['url']}")
                print(f"    放置位置: {file_path}")
                model_complete = False
        
        if model_complete:
            print(f"  → {model_name} 可以使用!")
        else:
            print(f"  → {model_name} 不完整")
    
    print("\n" + "="*80)
    print(f"总结: {available_models}/{total_models} 个模型文件可用")
    
    if available_models < total_models:
        print("\n缺失的模型文件需要手动下载并放置到正确位置。")
        print("文件名必须是哈希值，例如: sha256:3346b9cc551c9902fbf3b203935e933592b22e042365f58321c17fc12641fd6a")
    
    return available_models == total_models


if __name__ == "__main__":
    print("OSAM离线模型设置工具")
    print("适用于Windows环境")
    print("-" * 40)
    
    try:
        all_available = check_model_files()
        
        if all_available:
            print("\n🎉 所有模型文件都已准备就绪!")
            print("你可以在离线环境下使用osam了。")
        else:
            print("\n⚠️  还有模型文件缺失，请按照上面的说明下载。")
            
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)
