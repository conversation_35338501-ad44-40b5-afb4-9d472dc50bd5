import dataclasses
import os

import gdown
from loguru import logger


@dataclasses.dataclass
class Blob:
    url: str
    hash: str

    @property
    def path(self):
        return os.path.expanduser(f"~/.cache/osam/models/blobs/{self.hash}")

    @property
    def size(self):
        if os.path.exists(self.path):
            return os.stat(self.path).st_size
        else:
            return None

    @property
    def modified_at(self):
        if os.path.exists(self.path):
            return os.stat(self.path).st_mtime
        else:
            return None

    def pull(self):
        # Check if file already exists and has correct hash
        if os.path.exists(self.path):
            logger.debug("Model file already exists: {path!r}", path=self.path)
            # Optionally verify hash if needed
            try:
                import hashlib
                with open(self.path, 'rb') as f:
                    file_hash = hashlib.sha256(f.read()).hexdigest()
                expected_hash = self.hash.replace('sha256:', '')
                if file_hash == expected_hash:
                    logger.debug("Model file hash verified: {path!r}", path=self.path)
                    return
                else:
                    logger.warning("Model file hash mismatch, will re-download: {path!r}", path=self.path)
            except Exception as e:
                logger.warning("Failed to verify hash for {path!r}: {e}", path=self.path, e=e)

        # Only download if file doesn't exist or hash verification failed
        try:
            gdown.cached_download(url=self.url, path=self.path, hash=self.hash)
        except Exception as e:
            if os.path.exists(self.path):
                logger.warning("Download failed but file exists, proceeding: {path!r}", path=self.path)
            else:
                logger.error("Download failed and file does not exist: {path!r}", path=self.path)
                raise

    def remove(self):
        if os.path.exists(self.path):
            logger.debug("Removing blob {path!r}", path=self.path)
            os.remove(self.path)
        else:
            logger.warning("Blob {path!r} not found", path=self.path)
