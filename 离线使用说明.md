# OSAM离线使用说明

## 问题描述
在离线的Windows电脑上使用labelme的SAM功能时，osam库会尝试下载模型文件，导致程序崩溃。

## 解决方案

### 方案1：修改osam库源代码（推荐）

1. **找到osam库的安装位置**
   ```
   # 通常在以下位置之一：
   # Python安装目录/Lib/site-packages/osam/
   # 或者虚拟环境/Lib/site-packages/osam/
   ```

2. **修改 `types/_blob.py` 文件**
   
   将第31-32行的 `pull` 方法替换为：
   ```python
   def pull(self):
       # Check if file already exists and has correct hash
       if os.path.exists(self.path):
           logger.debug("Model file already exists: {path!r}", path=self.path)
           # Optionally verify hash if needed
           try:
               import hashlib
               with open(self.path, 'rb') as f:
                   file_hash = hashlib.sha256(f.read()).hexdigest()
               expected_hash = self.hash.replace('sha256:', '')
               if file_hash == expected_hash:
                   logger.debug("Model file hash verified: {path!r}", path=self.path)
                   return
               else:
                   logger.warning("Model file hash mismatch, will re-download: {path!r}", path=self.path)
           except Exception as e:
               logger.warning("Failed to verify hash for {path!r}: {e}", path=self.path, e=e)
       
       # Only download if file doesn't exist or hash verification failed
       try:
           gdown.cached_download(url=self.url, path=self.path, hash=self.hash)
       except Exception as e:
           if os.path.exists(self.path):
               logger.warning("Download failed but file exists, proceeding: {path!r}", path=self.path)
           else:
               logger.error("Download failed and file does not exist: {path!r}", path=self.path)
               raise
   ```

### 方案2：使用离线补丁（无需修改源代码）

1. **将 `offline_patch.py` 复制到你的项目中**

2. **在导入osam之前应用补丁**
   ```python
   import offline_patch
   offline_patch.apply_offline_patch()
   import osam
   ```

### 方案3：手动设置模型文件

1. **运行检查脚本**
   ```bash
   python setup_models_windows.py
   ```

2. **根据输出下载缺失的模型文件**

3. **将模型文件放置到正确位置**
   - Windows缓存目录：`%USERPROFILE%/.cache/osam/models/blobs/`
   - 文件名格式：`sha256:哈希值`

## 模型文件列表

### SAM模型
- **Sam100m** (推荐用于快速处理)
  - encoder: `sha256:3346b9cc551c9902fbf3b203935e933592b22e042365f58321c17fc12641fd6a`
  - decoder: `sha256:edbcf1a0afaa55621fb0abe6b3db1516818b609ea9368f309746a3afc68f7613`

- **Sam300m** (平衡性能和质量)
  - encoder: `sha256:f7158a4a1fe7f670ef47ea2f7f852685425c1ed6caa40f5df86cbe2b0502034f`
  - decoder: `sha256:552ebb23bf52c5e5b971ac710d1eb8dccfd88b36cc6aff881d1536d1662e6d7b`

- **Sam600m** (最高质量)
  - encoder: `sha256:a5c745fd4279efc5e5436b412200e983dafc2249ce172af6cc0002a71bb5f485`
  - decoder: `sha256:020b385a45ffe51097e1acd10cd791075a86171153505f789a793bc382eef210`

### SAM2模型
- **Sam2Tiny** (最快)
  - encoder: `sha256:5557482c56565f6a6c8206874b1a11c392cef8a1766477bf035b919092f2b619`
  - decoder: `sha256:11a2c86fabbea9d0268213a9205c99a7f7e379caa0493bd13f5cca8ffaae6777`

- **Sam2Small**
  - encoder: `sha256:06016c6dfb146ce10e4dadfdf49e88a05c8d1f97a6b7c57e150e60d2d46a72e7`
  - decoder: `sha256:153aaef5047a3b95285d90cbb39dad6c7b5821bfd944dbf56483f3f735936941`

- **Sam2BasePlus** (推荐)
  - encoder: `sha256:ce95c44082b4532c25ae01e11da3c9337dab7b04341455c09ae599dc9ae5c438`
  - decoder: `sha256:2ad091af889b20ad2035503b4355cd8924fcf0e29fa6536924c48dc220ecdc56`

- **Sam2Large** (最高质量)
  - encoder: `sha256:ab676f957528918496990f242163fd6b41a7222ae255862e846d9ab35115c12e`
  - decoder: `sha256:a3ebc6b8e254bd4ca1346901b9472bc2fae9e827cfd67d67e162d0ae2b1ec9a0`

### EfficientSAM模型
- **EfficientSam10m**
  - encoder: `sha256:7a73ee65aa2c37237c89b4b18e73082f757ffb173899609c5d97a2bbd4ebb02d`
  - decoder: `sha256:e1afe46232c3bfa3470a6a81c7d3181836a94ea89528aff4e0f2d2c611989efd`

- **EfficientSam30m**
  - encoder: `sha256:4cacbb23c6903b1acf87f1d77ed806b840800c5fcd4ac8f650cbffed474b8896`
  - decoder: `sha256:4727baf23dacfb51d4c16795b2ac382c403505556d0284e84c6ff3d4e8e36f22`

## 下载链接

所有模型文件可以从以下GitHub releases下载：

- SAM模型：https://github.com/wkentaro/labelme/releases/tag/sam-20230416
- SAM2模型：https://github.com/wkentaro/osam/releases/tag/sam2.1
- EfficientSAM模型：https://github.com/labelmeai/efficient-sam/releases/tag/onnx-models-20231225

## 验证安装

运行以下Python代码验证模型是否正确安装：

```python
import os

cache_dir = os.path.expanduser("~/.cache/osam/models/blobs")
print(f"缓存目录: {cache_dir}")

if os.path.exists(cache_dir):
    files = os.listdir(cache_dir)
    print(f"找到 {len(files)} 个模型文件")
    for f in files:
        if f.startswith('sha256:'):
            print(f"  {f}")
else:
    print("缓存目录不存在")
```

## 注意事项

1. **文件名格式**：模型文件必须以 `sha256:` 开头，后跟完整的哈希值
2. **目录结构**：确保目录结构为 `~/.cache/osam/models/blobs/`
3. **权限**：确保Python进程有读取模型文件的权限
4. **哈希验证**：建议验证下载文件的哈希值以确保完整性

## 故障排除

如果仍然遇到问题：

1. 检查Python环境和依赖包是否完整
2. 确认模型文件路径和权限
3. 查看osam的日志输出
4. 尝试使用不同的SAM模型（如从Sam600m改为Sam100m）
