#!/usr/bin/env python3
"""
离线模式补丁 - 用于在没有网络连接的环境下使用osam库

使用方法：
1. 在导入osam之前运行此补丁
2. 或者直接修改osam库的源代码

示例：
import offline_patch
offline_patch.apply_offline_patch()
import osam
"""

import os
import hashlib
from loguru import logger


def verify_file_hash(file_path, expected_hash):
    """验证文件哈希值"""
    try:
        with open(file_path, 'rb') as f:
            file_hash = hashlib.sha256(f.read()).hexdigest()
        expected_hash_clean = expected_hash.replace('sha256:', '')
        return file_hash == expected_hash_clean
    except Exception:
        return False


def offline_pull(self):
    """离线版本的pull方法"""
    if os.path.exists(self.path):
        logger.info("Using existing model file: {path!r}", path=self.path)
        
        # 验证哈希值（可选）
        if verify_file_hash(self.path, self.hash):
            logger.debug("Model file hash verified: {path!r}", path=self.path)
        else:
            logger.warning("Model file hash verification failed, but proceeding in offline mode: {path!r}", path=self.path)
        return
    else:
        # 文件不存在，在离线模式下抛出友好的错误信息
        raise FileNotFoundError(
            f"Model file not found in offline mode: {self.path}\n"
            f"Expected hash: {self.hash}\n"
            f"Please download the model file manually and place it at the correct location.\n"
            f"You can download from: {self.url}"
        )


def apply_offline_patch():
    """应用离线模式补丁"""
    try:
        # 尝试导入osam的Blob类
        from osam.types._blob import Blob
        
        # 替换pull方法
        Blob.pull = offline_pull
        logger.info("Offline patch applied successfully!")
        
        # 也可以选择完全禁用网络相关的导入
        import sys
        
        # 创建一个假的gdown模块，防止网络调用
        class FakeGdown:
            @staticmethod
            def cached_download(*args, **kwargs):
                raise RuntimeError("Network access disabled in offline mode")
        
        # 如果gdown已经被导入，替换它
        if 'gdown' in sys.modules:
            sys.modules['gdown'].cached_download = FakeGdown.cached_download
            
    except ImportError:
        logger.warning("osam not found, patch will be applied when osam is imported")


def create_offline_blob_class():
    """创建一个完全离线的Blob类"""
    import dataclasses
    import os
    from loguru import logger
    
    @dataclasses.dataclass
    class OfflineBlob:
        url: str
        hash: str

        @property
        def path(self):
            return os.path.expanduser(f"~/.cache/osam/models/blobs/{self.hash}")

        @property
        def size(self):
            if os.path.exists(self.path):
                return os.stat(self.path).st_size
            else:
                return None

        @property
        def modified_at(self):
            if os.path.exists(self.path):
                return os.stat(self.path).st_mtime
            else:
                return None

        def pull(self):
            """离线版本的pull方法"""
            if os.path.exists(self.path):
                logger.info("Using existing model file: {path!r}", path=self.path)
                
                # 验证哈希值
                if verify_file_hash(self.path, self.hash):
                    logger.debug("Model file hash verified: {path!r}", path=self.path)
                else:
                    logger.warning("Model file hash verification failed, but proceeding: {path!r}", path=self.path)
                return
            else:
                raise FileNotFoundError(
                    f"Model file not found: {self.path}\n"
                    f"Expected hash: {self.hash}\n"
                    f"Download URL: {self.url}\n"
                    f"Please download manually and place at the correct location."
                )

        def remove(self):
            if os.path.exists(self.path):
                logger.debug("Removing blob {path!r}", path=self.path)
                os.remove(self.path)
            else:
                logger.warning("Blob {path!r} not found", path=self.path)
    
    return OfflineBlob


if __name__ == "__main__":
    print("Applying offline patch...")
    apply_offline_patch()
    print("Offline patch applied!")
